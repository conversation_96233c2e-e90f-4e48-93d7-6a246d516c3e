﻿namespace SimpleBooks.PermissionAndSession.Authentication
{
    public class AuthenticationLoginValidationService : IAuthenticationValidationService
    {
        private readonly Session.ISession _session;

        public AuthenticationLoginValidationService(Session.ISession session)
        {
            _session = session;
        }

        public string GetStringToken()
        {
            string token = string.Empty;

            token = _session.LoginSessionResponse?.Token ?? string.Empty;

            return token;
        }

        public JwtSecurityToken? GetSecurityToken()
        {
            var token = GetStringToken();
            if (string.IsNullOrEmpty(token))
                return null;

            var accessToken = token.ToString().Replace(AuthenticationDefaults.AuthenticationScheme + " ", "");
            var tokenHandler = new JwtSecurityTokenHandler();
            var jwtToken = tokenHandler.ReadToken(accessToken) as JwtSecurityToken;

            return jwtToken;
        }

        public bool IsTokenValid(JwtSecurityToken jwtSecurityToken)
        {
            if (jwtSecurityToken is null)
                return false;

            if (jwtSecurityToken.ValidTo < DateTime.UtcNow)
                return false;

            return true;
        }

        public void SetTokenToHeader(string token)
        {
            throw new NotImplementedException();
        }

        public SessionIdentity GetSessionIdentity()
        {
            var jwtToken = GetSecurityToken();

            if (jwtToken is null)
                throw new UnauthorizedAccessException();

            var claims = jwtToken.Claims;
            SessionIdentity sessionIdentity = new SessionIdentity()
            {
                EmployeeId = Ulid.Parse(claims.Single(x => x.Type == JwtRegisteredClaimNames.NameId).Value),
            };

            return sessionIdentity;
        }
    }
}
