﻿namespace SimpleBooks.Repositories.EF.Factory.Config.HR
{
    public class EmployeeConfiguration : IEntityTypeConfiguration<EmployeeModel>
    {
        public void Configure(EntityTypeBuilder<EmployeeModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasIndex(x => x.EmployeeName).IsUnique();

            builder.HasOne(d => d.User).WithOne(p => p.Employee)
                .HasForeignKey<EmployeeModel>(d => d.UserId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasData(Data.GetEmployees());
        }
    }
}
