﻿namespace SimpleBooks.Repositories.EF.Factory.Config.User
{
    public class SettingConfiguration : IEntityTypeConfiguration<SettingModel>
    {
        public void Configure(EntityTypeBuilder<SettingModel> builder)
        {
            builder.HasKey(x => new { x.Id });

            builder.HasOne(d => d.User).WithOne(p => p.Setting)
                .HasForeignKey<SettingModel>(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasData(Data.GetSettings());
        }
    }
}
