﻿@model LoginRequest

@{
    ViewData["Title"] = "Login";
    Layout = "~/Views/Shared/_LayoutLogin.cshtml";
}

<form asp-controller="Authentication" enctype="multipart/form-data" class="col-12 col-md-7 col-lg-6">
    <div class="card card-shadow border-0 rounded-3">
        <div class="card-body p-4">
            <div class="row g-6">
                <div class="col-12">
                    <div class="text-center">
                        <h1 class="fw-bold mb-2">Sign In</h1>
                        <p class="text-muted">Login to your account</p>
                    </div>
                </div>
            </div>
            <hr />
            <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
            <div class="form-group mb-2">
                <label asp-for="Email" class="form-label mt-2"></label>
                <input type="email" class="form-control" asp-for="Email" placeholder="Email">
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
            <div class="form-group mb-2">
                <label asp-for="Password" class="form-label mt-2"></label>
                <input type="password" class="form-control" asp-for="Password" placeholder="Password">
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-primary btn-block btn-lg fs-6 fw-bolder py-3 w-100">Login</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}