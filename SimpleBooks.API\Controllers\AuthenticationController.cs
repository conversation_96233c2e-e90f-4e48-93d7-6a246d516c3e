﻿namespace SimpleBooks.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly IAuthenticationService _authenticationService;

        public AuthenticationController(IAuthenticationService authenticationService)
        {
            _authenticationService = authenticationService;
        }

        [HttpPost("Login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest model)
        {
            try
            {
                var result = await _authenticationService.Login(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("RefreshTokenAsync")]
        public async Task<IActionResult> RefreshTokenAsync()
        {
            var refreshToken = Request.Cookies[AuthenticationDefaults.RefreshTokenCookie];

            if (string.IsNullOrEmpty(refreshToken))
                return BadRequest("Token is required!");

            try
            {
                RefreshTokenRequest refreshTokenRequest = new RefreshTokenRequest() { Token = refreshToken };
                var result = await _authenticationService.RefreshTokenAsync(refreshTokenRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpGet("RevokeTokenAsync")]
        public async Task<IActionResult> RevokeTokenAsync(string revokeToken)
        {
            var token = revokeToken ?? Request.Cookies[AuthenticationDefaults.RefreshTokenCookie];

            if (string.IsNullOrEmpty(token))
                return BadRequest("Token is required!");

            try
            {
                var result = await _authenticationService.RevokeTokenAsync(token);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
    }
}
