﻿namespace SimpleBooks.PermissionAndSession.DI
{
    public static class InjectAuthenticationLayerServices
    {
        public static IServiceCollection InjectAuthenticationServices(this IServiceCollection services)
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("JWTAppSettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("SessionAppSettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("DataBaseAppSettings.json", optional: false, reloadOnChange: true)
                .Build();

            JWTAppSettings jwtAppSettings = configuration.GetSection("JWT").Get<JWTAppSettings>()
                ?? throw new Exception("Need To Implement JWTAppSettings");
            services.AddSingleton(jwtAppSettings);

            DataBaseAppSettings dataBaseAppSettings = configuration.GetSection("ConnectionStrings").Get<DataBaseAppSettings>()
                ?? throw new Exception("Need To Implement DataBaseAppSettings");
            services.AddSingleton(dataBaseAppSettings);

            SessionAppSettings sessionAppSettings = configuration.GetSection("Session").Get<SessionAppSettings>()
                ?? throw new Exception("Need To Implement SessionAppSettings");
            services.AddSingleton(sessionAppSettings);

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultForbidScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultSignInScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultSignOutScheme = JwtBearerDefaults.AuthenticationScheme;
            //}).AddCookie(IdentityConstants.ApplicationScheme, options =>
            //{
            //    options.Cookie.Name = AuthenticationDefaults.AuthenticationCookie;
            //    options.Cookie.HttpOnly = true;
            //    options.ExpireTimeSpan = TimeSpan.FromDays(7);
            //    options.SlidingExpiration = true;
            //    options.LoginPath = "/Authentication/Login";
            //    options.LogoutPath = "/Authentication/Logout";
            //    options.AccessDeniedPath = "/Authentication/AccessDenied";
            }).AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                // Set up token mechanism
                options.TokenValidationParameters = new TokenValidationParameters()
                {
                    ValidateIssuer = true,
                    ValidIssuer = jwtAppSettings.Issuer,
                    ValidateAudience = true,
                    ValidAudience = jwtAppSettings.Audience,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtAppSettings.Key)),
                    ClockSkew = TimeSpan.Zero,
                };

                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                            context.Response.Headers.Append("Token-Expired", "true");
                        return Task.CompletedTask;
                    },
                    OnMessageReceived = context =>
                    {
                        if (context.Request.Headers.ContainsKey(AuthenticationDefaults.AuthenticationHeader))
                        {
                            string bearerPrefix = AuthenticationDefaults.AuthenticationScheme + " ";

                            var bearerToken = context.Request.Headers[AuthenticationDefaults.AuthenticationHeader].ElementAt(0);
                            if (!string.IsNullOrEmpty(bearerToken))
                            {
                                var token = bearerToken.StartsWith(bearerPrefix) ? bearerToken.Substring(bearerPrefix.Length) : bearerToken;
                                context.Token = token;
                            }
                        }
                        return Task.CompletedTask;
                    }
                };
            });

            services.AddAuthorization();

            return services;
        }
    }
}
