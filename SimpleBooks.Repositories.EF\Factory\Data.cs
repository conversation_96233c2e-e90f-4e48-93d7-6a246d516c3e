﻿namespace SimpleBooks.Repositories.EF.Factory
{
    public static class Data
    {
        private static Ulid Employee1Id = Ulid.Parse("01JRK7Q6NDYC4AEEPQBWNX7V2G");
        private static Ulid Employee2Id = Ulid.Parse("01JRK7QNHFMD61A648A9N09122");
        private static Ulid User1Id = Ulid.Parse("01JRK7KJVTERSKVHZGGX4DQ4M0");
        private static Ulid User2Id = Ulid.Parse("01JRK7KXPW5RY0FPRAPV06E9G9");
        private static Ulid SettingId = Ulid.Parse("01JM898XB2RH67PPFANBQV0KRG");
        private static Ulid ScreensAccessProfile1Id = Ulid.Parse("01JVDCZD2ST67FD5DFX2KECWKR");
        private static Ulid ScreensAccessProfile2Id = Ulid.Parse("01JVDCZHG4FZWV2XFZ4EEYJN3Z");

        public static List<EmployeeModel> GetEmployees()
        {
            return new List<EmployeeModel>()
            {
                new EmployeeModel()
                {
                    Id = Employee1Id,
                    EmployeeName = "Admin",
                    EmployeeEMail="<EMAIL>",
                    EmployeePhone="1234567890",
                    EmployeeIsRep = false,
                    UserId = User1Id,
                },
                new EmployeeModel()
                {
                    Id = Employee2Id,
                    EmployeeName = "User",
                    EmployeeEMail="<EMAIL>",
                    EmployeePhone="1234567890",
                    EmployeeIsRep = false,
                    UserId = User2Id,
                },
            };
        }

        public static List<UserModel> GetUsers()
        {
            IPasswordHasher<UserModel> passwordHasher = new PasswordHasher<UserModel>();

            UserModel user1 = new UserModel()
            {
                Id = User1Id,
                EmployeeId = Employee1Id,
                UserName = "Admin",
                Email = "<EMAIL>",
                UserTypeId = UserTypeEnumeration.Admin.Value,
                ScreensAccessProfileId = ScreensAccessProfile1Id,
            };
            user1.PasswordHash = passwordHasher.HashPassword(user1, "123456");
            user1.NormalizedEmail = user1.Email.ToUpper();
            user1.NormalizedUserName = user1.UserName.ToUpper();

            UserModel user2 = new UserModel()
            {
                Id = User2Id,
                EmployeeId = Employee2Id,
                UserName = "User",
                Email = "<EMAIL>",
                UserTypeId = UserTypeEnumeration.User.Value,
                ScreensAccessProfileId = ScreensAccessProfile2Id,
            };
            user2.PasswordHash = passwordHasher.HashPassword(user2, "123456");
            user2.NormalizedEmail = user2.Email.ToUpper();
            user2.NormalizedUserName = user2.UserName.ToUpper();

            return new List<UserModel>() { user1, user2 };
        }

        public static List<ScreensAccessProfileModel> GetScreensAccessProfiles()
        {
            return new List<ScreensAccessProfileModel>()
            {
                new ScreensAccessProfileModel()
                {
                    Id = ScreensAccessProfile1Id,
                    ScreensAccessProfileName = "Admin Profile",
                    IsActive = true,
                },
                new ScreensAccessProfileModel()
                {
                    Id = ScreensAccessProfile2Id,
                    ScreensAccessProfileName = "User Profile",
                    IsActive = true,
                }
            };
        }

        public static List<ScreensAccessProfileDetailsModel> GetScreensAccessProfileDetails()
        {
            List<ScreensAccessProfileDetailsModel> list = new List<ScreensAccessProfileDetailsModel>();

            var list1 = Screens.GetScreens.Select(x => new ScreensAccessProfileDetailsModel()
            {
                ScreensAccessProfileId = ScreensAccessProfile1Id,
                Id = Generator("1" + x.ScreenId.ToString()),
                ScreenId = x.ScreenId,
                CanShow = x.CanShow,
                CanOpen = x.CanOpen,
                CanAdd = x.CanAdd,
                CanEdit = x.CanEdit,
                CanDelete = x.CanDelete,
                CanPrint = x.CanPrint,
            });

            var list2 = Screens.GetScreens.Select(x => new ScreensAccessProfileDetailsModel()
            {
                ScreensAccessProfileId = ScreensAccessProfile2Id,
                Id = Generator("2" + x.ScreenId.ToString()),
                ScreenId = x.ScreenId,
                CanShow = x.CanShow,
                CanOpen = x.CanOpen,
                CanAdd = x.CanAdd,
                CanEdit = x.CanEdit,
                CanDelete = x.CanDelete,
                CanPrint = x.CanPrint,
            });

            list.AddRange(list1);
            list.AddRange(list2);

            return list;
        }

        public static List<SettingModel> GetSettings()
        {
            return new List<SettingModel>()
            {
                new SettingModel()
                {
                    Id = SettingId,
                    UserId = User1Id,
                },
            };
        }

        public static List<TransactionTypeModel> GetTransactionTypes()
        {
            return TransactionTypeEnumeration.TransactionTypeEnumerations.Select(x => new TransactionTypeModel()
            {
                Id = x.Value,
                TransactionTypeName = x.Name,
            }).ToList();
        }

        public static List<BeneficiaryTypeModel> GetBeneficiaryTypes()
        {
            return BeneficiaryTypeEnumeration.BeneficiaryTypeEnumerations.Select(x => new BeneficiaryTypeModel()
            {
                Id = x.Value,
                BeneficiaryTypeName = x.Name,
            }).ToList();
        }

        public static List<ProductTypeModel> GetProductTypes()
        {
            return ProductTypeEnumeration.ProductTypeEnumerations.Select(x => new ProductTypeModel()
            {
                Id = x.Value,
                ProductTypeName = x.Name,
            }).ToList();
        }

        public static List<TaxTypeModel> GetTaxTypes()
        {
            return TaxTypeEnumeration.TaxTypeEnumerations.Select(x => new TaxTypeModel()
            {
                Id = x.Value,
                Code = x.Name,
                Desc_en = x.Desc_en,
                Desc_ar = x.Desc_ar,
                IsAddition = x.IsAddition,
            }).ToList();
        }

        public static List<TaxSubTypeModel> GetTaxSubTypes()
        {
            return TaxSubTypeEnumeration.TaxSubTypeEnumerations.Select(x => new TaxSubTypeModel()
            {
                Id = x.Value,
                Code = x.Name,
                Desc_en = x.Desc_en,
                Desc_ar = x.Desc_ar,
                TaxTypeId = x.TaxTypeEnumeration.Value,
            }).ToList();
        }

        public static List<CheckStatusModel> GetCheckStatuss()
        {
            return CheckStatusEnumeration.CheckStatusEnumerations.Select(x => new CheckStatusModel()
            {
                Id = x.Value,
                CheckStatusName = x.Name,
            }).ToList();
        }

        private static Ulid Generator(string? inputString = null)
        {
            try
            {
                DateTimeOffset dateTimeOffset = DateTimeOffset.MinValue;
                inputString ??= new Random().Next(1, 100000).ToString();
                byte[] randomnessBytes = GetFixedRandomness(inputString, 10);
                Ulid newUlid = Ulid.NewUlid(dateTimeOffset, randomnessBytes);
                return newUlid;
            }
            catch
            {
                return Ulid.NewUlid();
            }
        }

        private static byte[] GetFixedRandomness(string input, int length)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(input);

            if (bytes.Length > length)
                return bytes[..length]; // Trim if too long
            else if (bytes.Length < length)
            {
                byte[] padded = new byte[length];
                Array.Copy(bytes, padded, bytes.Length);
                return padded; // Pad if too short
            }

            return bytes;
        }
    }
}
