﻿namespace SimpleBooks.Services.Server.Authentication
{
    public class AuthenticationService : IAuthenticationService
    {
        //Fields
        private readonly IUnitOfWork _repository;
        private readonly JWTAppSettings _jwtAppSettings;
        private readonly SessionAppSettings _sessionAppSettings;
        private readonly UserManager<UserModel> _userManager;
        private readonly SignInManager<UserModel> _signInManager;

        //Constructor
        public AuthenticationService(
            IUnitOfWork repository,
            JWTAppSettings jwtAppSettings,
            SessionAppSettings sessionAppSettings,
            UserManager<UserModel> userManager,
            SignInManager<UserModel> signInManager)
        {
            _repository = repository;
            _jwtAppSettings = jwtAppSettings;
            _sessionAppSettings = sessionAppSettings;
            _userManager = userManager;
            _signInManager = signInManager;
        }

        public async Task<ServiceResult<LoginSessionResponse>> Login(LoginRequest loginRequest)
        {
            try
            {
                // Use Identity's UserManager to find and validate user
                var user = await _userManager.FindByEmailAsync(loginRequest.Email);
                if (user == null)
                {
                    return ServiceResult<LoginSessionResponse>.Failure("Invalid username or password", System.Net.HttpStatusCode.Unauthorized);
                }

                // Use SignInManager to check password
                var signInResult = await _signInManager.CheckPasswordSignInAsync(user, loginRequest.Password, lockoutOnFailure: true);
                if (!signInResult.Succeeded)
                {
                    string errorMessage = "Invalid username or password";
                    if (signInResult.IsLockedOut)
                        errorMessage = "Account is locked out";
                    else if (signInResult.IsNotAllowed)
                        errorMessage = "Sign in not allowed";

                    return ServiceResult<LoginSessionResponse>.Failure(errorMessage, System.Net.HttpStatusCode.Unauthorized);
                }

                // Get employee information using the existing repository pattern
                RepositorySpecifications<UserModel> userRepositorySpecifications = new RepositorySpecifications<UserModel>()
                {
                    SearchValue = x => x.Id == user.Id,
                    Includes = x => x.Include(xx => xx.Employee)
                        .Include(xx => xx.Setting)
                        .Include(xx => xx.ScreensAccessProfile)
                        .ThenInclude(xx => xx.ScreensAccessProfileDetails)
                };

                var userModel = await _repository.User.GetAsync(userRepositorySpecifications);
                if (userModel == null)
                {
                    return ServiceResult<LoginSessionResponse>.Failure("Employee record not found", System.Net.HttpStatusCode.Unauthorized);
                }

                // Create employee login session response
                EmployeeLoginSessionResponse employee = new EmployeeLoginSessionResponse()
                {
                    Id = userModel.Employee.Id,
                    EmployeeName = userModel.Employee.EmployeeName,
                    User = new UserLoginSessionResponse()
                    {
                        Id = userModel.Id,
                        UserName = userModel.UserName ?? userModel.Email ?? string.Empty,
                        UserTypeId = userModel.UserTypeId,
                        ScreensAccessProfile = new ScreensAccessProfileLoginSessionResponse()
                        {
                            Id = userModel.ScreensAccessProfile.Id,
                            ScreensAccessProfileName = userModel.ScreensAccessProfile.ScreensAccessProfileName,
                            CreatedBy = userModel.ScreensAccessProfile.CreatedBy,
                            CreatedAt = userModel.ScreensAccessProfile.CreatedAt,
                            DeletedBy = userModel.ScreensAccessProfile.DeletedBy,
                            DeletedAt = userModel.ScreensAccessProfile.DeletedAt,
                            ModifaiedBy = userModel.ScreensAccessProfile.ModifaiedBy,
                            ModifaiedAt = userModel.ScreensAccessProfile.ModifaiedAt,
                            IsActive = userModel.ScreensAccessProfile.IsActive,
                            ScreensAccessProfileDetails = userModel.ScreensAccessProfile.ScreensAccessProfileDetails,
                        },
                    },
                    Setting = new SettingLoginSessionResponse()
                    {
                        Id = userModel.Setting.Id,
                    },
                };

                SecurityToken jwtSecurityToken = await CreateJwtTokenAsync(userModel);

                string token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
                DateTime expiresOn = jwtSecurityToken.ValidTo;

                var refreshTokens = _repository.RefreshToken.RefreshTokensForEmployee(employee.Id);

                string refreshToken = string.Empty;
                DateTime refreshTokenExpiration = DateTime.UtcNow.AddDays(10);

                if (refreshTokens.Any(t => t.IsActive))
                {
                    RefreshTokenModel? activeRefreshToken = refreshTokens.FirstOrDefault(t => t.IsActive);
                    if (activeRefreshToken != null)
                    {
                        refreshToken = activeRefreshToken.Token;
                        refreshTokenExpiration = activeRefreshToken.ExpiresOn;
                    }
                }
                else
                {
                    RefreshTokenModel refreshTokenModel = GenerateRefreshToken();
                    refreshToken = refreshTokenModel.Token;
                    refreshTokenExpiration = refreshTokenModel.ExpiresOn;
                    refreshTokenModel.UserId = employee.User.Id;
                    await _repository.RefreshToken.AddAsync(refreshTokenModel);
                }

                RefreshTokenResponse refreshTokenResponse = new RefreshTokenResponse()
                {
                    Token = token,
                    TokenExpiration = expiresOn,
                    RefreshToken = refreshToken,
                    RefreshTokenExpiration = refreshTokenExpiration,
                };
                LoginSessionResponse loginSessionResponse = new LoginSessionResponse()
                {
                    Token = token,
                    ExpiresOn = expiresOn,
                    RefreshToken = refreshTokenResponse,
                    Employee = employee,
                };
                return loginSessionResponse;
            }
            catch (Exception ex)
            {
                return ServiceResult<LoginSessionResponse>.Failure("Login failed\n" + ex.Message, System.Net.HttpStatusCode.Unauthorized);
            }
        }

        public async Task<ServiceResult<RefreshTokenResponse>> RefreshTokenAsync(RefreshTokenRequest refreshTokenRequest)
        {
            try
            {
                await Task.Delay(10);

                EmployeeLoginSessionResponse employee = _repository.User.ReLogin(refreshTokenRequest.Token);

                if (employee == null)
                    throw new Exception("Invalid token");

                RefreshTokenModel? refreshToken = _repository.RefreshToken.RefreshTokensForToken(refreshTokenRequest.Token);

                if (refreshToken == null || !refreshToken.IsActive)
                    throw new Exception("Inactive token");

                refreshToken.RevokedOn = DateTime.UtcNow;

                RefreshTokenModel newRefreshToken = GenerateRefreshToken();
                newRefreshToken.UserId = employee.User.Id;
                await _repository.RefreshToken.AddAsync(newRefreshToken);

                SecurityToken jwtSecurityToken = CreateJwtToken(employee, _jwtAppSettings);

                string newToken = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
                DateTime expiresOn = jwtSecurityToken.ValidTo;

                RefreshTokenResponse refreshTokenResponse = new RefreshTokenResponse()
                {
                    Token = newToken,
                    TokenExpiration = expiresOn,
                    RefreshToken = newRefreshToken.Token,
                    RefreshTokenExpiration = newRefreshToken.ExpiresOn,
                };
                return refreshTokenResponse;
            }
            catch (Exception ex)
            {
                return ServiceResult<RefreshTokenResponse>.Failure("RefreshToken failed\n" + ex.Message, System.Net.HttpStatusCode.Unauthorized);
            }
        }

        public async Task<ServiceResult<bool>> RevokeTokenAsync(string token)
        {
            RefreshTokenModel? refreshToken = _repository.RefreshToken.RefreshTokensForToken(token);

            if (refreshToken == null)
                return false;

            if (!refreshToken.IsActive)
                return false;

            refreshToken.RevokedOn = DateTime.UtcNow;

            await _repository.RefreshToken.UpdateAsync(refreshToken);
            return true;
        }

        private RefreshTokenModel GenerateRefreshToken()
        {
            byte[] randomNumber = new byte[32];

            using (var generator = RandomNumberGenerator.Create())
            {
                generator.GetBytes(randomNumber);

                return new RefreshTokenModel
                {
                    Token = Convert.ToBase64String(randomNumber),
                    ExpiresOn = DateTime.UtcNow.AddDays(10),
                    CreatedOn = DateTime.UtcNow
                };
            }
        }

        private SecurityToken CreateJwtToken(EmployeeLoginSessionResponse employee, JWTAppSettings jwt)
        {
            Claim[] claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub ,jwt.Subject),
                new Claim(JwtRegisteredClaimNames.Jti, Ulid.NewUlid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, EpochTime.GetIntDate(DateTime.UtcNow).ToString(), ClaimValueTypes.Integer64),
                new Claim(JwtRegisteredClaimNames.NameId, employee.Id.ToString()),
                new Claim(nameof(EmployeeLoginSessionResponse.EmployeeName), employee.EmployeeName),
                new Claim(ClaimTypes.Name, employee.User.UserName),
            };

            SymmetricSecurityKey symmetricSecurityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwt.Key));
            SigningCredentials signingCredentials = new SigningCredentials(symmetricSecurityKey, SecurityAlgorithms.HmacSha256);

            SecurityTokenDescriptor tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(jwt.DurationInMinutes),
                SigningCredentials = signingCredentials,
                Issuer = jwt.Issuer,
                Audience = jwt.Audience
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken jwtSecurityToken = tokenHandler.CreateToken(tokenDescriptor);

            return jwtSecurityToken;
        }

        private async Task<Claim[]> GetClaimsAsync(UserModel user)
        {
            var userClaims = await _userManager.GetClaimsAsync(user);
            var roles = await _userManager.GetRolesAsync(user);
            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Ulid.NewUlid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, EpochTime.GetIntDate(DateTime.UtcNow).ToString(), ClaimValueTypes.Integer64),
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(nameof(EmployeeLoginSessionResponse.EmployeeName), user.Employee?.EmployeeName ?? ""),
            };
            claims.AddRange(userClaims);
            claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));
            return claims.ToArray();
        }

        private async Task<SecurityToken> CreateJwtTokenAsync(UserModel user)
        {
            var claims = await GetClaimsAsync(user);

            SymmetricSecurityKey symmetricSecurityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtAppSettings.Key));
            SigningCredentials signingCredentials = new SigningCredentials(symmetricSecurityKey, SecurityAlgorithms.HmacSha256);

            SecurityTokenDescriptor tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(_jwtAppSettings.DurationInMinutes),
                SigningCredentials = signingCredentials,
                Issuer = _jwtAppSettings.Issuer,
                Audience = _jwtAppSettings.Audience
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            SecurityToken jwtSecurityToken = tokenHandler.CreateToken(tokenDescriptor);

            return jwtSecurityToken;
        }
    }
}
