namespace SimpleBooks.Repositories.EF.Factory.Config.User
{
    public class UserConfiguration : IEntityTypeConfiguration<UserModel>
    {
        public void Configure(EntityTypeBuilder<UserModel> builder)
        {
            // Remove explicit key configuration as it should be defined on the base class
            // builder.HasKey(x => new { x.Id });

            builder.HasIndex(x => x.UserName).IsUnique();

            builder.HasOne(d => d.ScreensAccessProfile).WithMany(p => p.Users)
                .HasForeignKey(d => d.ScreensAccessProfileId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasData(Data.GetUsers());
        }
    }
}
